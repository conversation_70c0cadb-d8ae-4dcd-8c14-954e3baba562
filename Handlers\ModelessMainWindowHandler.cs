﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Services;
using MEP.TraylorSwift.ViewModels;
using MEP.TraylorSwift.Views;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using MessageBox = System.Windows.MessageBox;

namespace MEP.TraylorSwift.Handlers
{
    public static class ModelessMainWindowHandler
    {
        #region Fields
        private static ModelessMainWindow _modelessMainWindow;
        private static RequestHandler _requestHandler;
        private static ExternalEvent _externalEvent;
        private static IServiceProvider _serviceProvider;

        // Static references for cross-form communication (preserving original pattern)
        public static UIDocument UIDocument { get; private set; }
        public static BecaActivityLoggerData ActivityLogger { get; private set; }
        #endregion

        #region Properties

        /// <summary>
        /// Indicates if the main window is currently open and visible
        /// </summary>
        public static bool IsWindowOpen => _modelessMainWindow != null && _modelessMainWindow.IsLoaded && _modelessMainWindow.IsVisible;

        /// <summary>
        /// Get the current service provider for dependency injection
        /// </summary>
        public static IServiceProvider ServiceProvider => _serviceProvider;

        #endregion

        #region Public Methods

        /// <summary>
        /// Show the main window (singleton pattern)
        /// This is the main entry point called from CableTrayLengthCommand
        /// </summary>
        /// <param name="logger">Activity logger for tracking operations</param>
        public static void ShowWindow(UIDocument uidoc, BecaActivityLoggerData logger)
        {
            try
            {
                // Store references for cross-form communication
                UIDocument = uidoc ?? throw new ArgumentNullException(nameof(uidoc));
                ActivityLogger = logger ?? throw new ArgumentNullException(nameof(logger));

                // Singleton pattern - only one main window allowed
                if (_modelessMainWindow == null)
                {
                    // Create new window
                    InitializeServices();
                    CreateModelessArchitecture();
                    CreateMainWindow();
                }
                else if (!_modelessMainWindow.IsVisible)
                {
                    // Window exists but is hidden - show it
                    _modelessMainWindow.Show();
                    _modelessMainWindow.Activate();
                    _modelessMainWindow.WindowState = WindowState.Normal;
                    _modelessMainWindow.Topmost = true;
                    _modelessMainWindow.Topmost = false;
                    _modelessMainWindow.Focus();
                    ActivityLogger?.Log("TrayloSwift main window shown", LogType.Information);
                }
                else
                {
                    // Window exists and is visible - bring to front
                    _modelessMainWindow.Activate();
                    _modelessMainWindow.WindowState = WindowState.Normal;
                    _modelessMainWindow.Topmost = true;
                    _modelessMainWindow.Topmost = false;
                    _modelessMainWindow.Focus();
                    ActivityLogger?.Log("TrayloSwift main window activated", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show TrayloSwift main window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Failed to open TrayloSwift", ex.Message);
            }
        }

        /// <summary>
        /// Close the main window and clean up resources
        /// </summary>
        public static void CloseWindow()
        {
            try
            {
                ActivityLogger?.Log("Closing TrayloSwift main window", LogType.Information);

                // Close and dispose main window
                _modelessMainWindow?.Close();
                _modelessMainWindow = null;

                // Dispose ExternalEvent
                _externalEvent?.Dispose();
                _externalEvent = null;

                // Clear references
                _requestHandler = null;
                _serviceProvider = null;
                ActivityLogger = null;

                // Note: ActivityLogger is set to null above, so no final log message
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Error closing TrayloSwift main window: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Wake up the main window after ExternalEvent processing
        /// Called by RequestHandler_PB6 to update UI state
        /// </summary>
        public static void WakeUpMainWindow()
        {
            try
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    // Wake up the main window
                    if (_modelessMainWindow?.DataContext is TrayTaggerViewModel trayTaggerViewModel)
                    {
                        trayTaggerViewModel.WakeUp();
                        ActivityLogger?.Log("Main window woken up", LogType.Information);
                    }

                    // Also wake up any open ConfigureTraySegmentWindow
                    var configureWindows = System.Windows.Application.Current.Windows
                        .OfType<Views.ConfigureTraySegmentWindow>()
                        .ToList();

                    foreach (var configureWindow in configureWindows)
                    {
                        if (configureWindow.ViewModel != null)
                        {
                            configureWindow.ViewModel.WakeUp();
                            ActivityLogger?.Log("Configure window woken up", LogType.Information);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to wake up main window: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initialize the dependency injection services
        /// </summary>
        private static void InitializeServices()
        {
            try
            {
                _serviceProvider = ServiceConfiguration.BuildServiceProvider(UIDocument, ActivityLogger);
                ActivityLogger?.Log("Services initialized successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize services", ex);
            }
        }

        /// <summary>
        /// Create the modeless architecture components
        /// </summary>
        private static void CreateModelessArchitecture()
        {
            try
            {
                _requestHandler = new RequestHandler(_serviceProvider, ActivityLogger);
                _externalEvent = ExternalEvent.Create(_requestHandler);
                ActivityLogger?.Log("Modeless architecture created successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create modeless architecture", ex);
            }
        }

        /// <summary>
        /// Create and show the main window
        /// </summary>
        private static void CreateMainWindow()
        {
            try
            {
                var trayTaggerViewModel = _serviceProvider.GetRequiredService<TrayTaggerViewModel>();
                trayTaggerViewModel.Initialize(_requestHandler, _externalEvent);

                _modelessMainWindow = new ModelessMainWindow(trayTaggerViewModel)
                {
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // Handle window closing
                _modelessMainWindow.Closed += OnMainWindowClosed;

                _modelessMainWindow.Show();
                ActivityLogger?.Log("Main window created and shown successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create main window", ex);
            }
        }

        /// <summary>
        /// Handle main window closed event
        /// </summary>
        private static void OnMainWindowClosed(object sender, EventArgs e)
        {
            CloseWindow();
        }

        /// <summary>
        /// Show error message to user
        /// </summary>
        private static void ShowErrorMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Error: {title} - {message}");
            }
        }

        /// <summary>
        /// Show info message to user
        /// </summary>
        /// <param name="title">Message title</param>
        /// <param name="message">Message content</param>
        private static void ShowInfoMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Info: {title} - {message}");
            }
        }

        /// <summary>
        /// Refresh main window data after circuit editing
        /// Mimics the behavior from original WinForms implementation
        /// </summary>
        private static void RefreshMainWindowData()
        {
            try
            {
                if (_modelessMainWindow?.DataContext is TrayTaggerViewModel trayTaggerViewModel)
                {
                    ActivityLogger?.Log("Main window data refreshed", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to refresh main window data: {ex.Message}", LogType.Error);
            }
        }

        #endregion
    }
}
