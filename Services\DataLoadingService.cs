using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using Microsoft.AspNetCore.Components.Server.Circuits;
using System;
using System.Collections.Generic;
using System.Linq;
using View = Autodesk.Revit.DB.View;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of IDataLoadingService for loading and initializing TS_Data from Revit document
    /// </summary>
    public class DataLoadingService : IDataLoadingService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private readonly ITrayAnalysisService _trayAnalysisService;
        private readonly ICableDetectionService _cableDetectionService;
        private readonly IRevitParameterService _parameterService;
        private readonly ISpatialIndexService _spatialIndexService;
        private DataLoadingStatistics _lastLoadingStatistics;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the data loading service
        /// </summary>
        public DataLoadingService(
            Document document,
            BecaActivityLoggerData logger,
            ITrayAnalysisService trayAnalysisService,
            ICableDetectionService cableDetectionService,
            IRevitParameterService parameterService,
            ISpatialIndexService spatialIndexService)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
            _trayAnalysisService = trayAnalysisService ?? throw new ArgumentNullException(nameof(trayAnalysisService));
            _cableDetectionService = cableDetectionService ?? throw new ArgumentNullException(nameof(cableDetectionService));
            _parameterService = parameterService ?? throw new ArgumentNullException(nameof(parameterService));
            _spatialIndexService = spatialIndexService ?? throw new ArgumentNullException(nameof(spatialIndexService));
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load complete TS_Data from the Revit document
        /// </summary>
        public TS_Data LoadTSDataFromDocument()
        {
            return LoadTSDataFromDocument(DataLoadingOptions.Default);
        }

        /// <summary>
        /// Load complete TS_Data from the Revit document with options
        /// </summary>
        public TS_Data LoadTSDataFromDocument(DataLoadingOptions options)
        {
            _lastLoadingStatistics = new DataLoadingStatistics
            {
                DocumentTitle = _document.Title,
                DocumentPath = _document.PathName
            };

            try
            {
                _logger?.Log("Starting TS_Data loading from document", LogType.Information);

                var tsData = new TS_Data(_document);

                // Load cable trays
                if (options.LoadCableTrays)
                {
                    var traySegments = LoadExistingTraySegments();
                    foreach (var traySegment in traySegments)
                    {
                        tsData.AddTraySegment(traySegment);
                    }
                    _lastLoadingStatistics.TraySegmentsWithCableData = traySegments.Count;
                }

                // Load cables from circuits
                if (options.LoadCircuits)
                {
                    var cables = LoadAllCablesFromCircuits();
                    foreach (var cable in cables.Values)
                    {
                        tsData.AddOrUpdateCable(cable);
                    }
                    _lastLoadingStatistics.CablesCreated = cables.Count;
                }

                // Load electrical equipment
                if (options.LoadElectricalEquipment)
                {
                    tsData.ElectricalEquipment = LoadElectricalEquipment();
                    _lastLoadingStatistics.ElectricalEquipmentFound = tsData.ElectricalEquipment.Count;
                }

                // Load levels
                tsData.Levels = LoadLevels();
                _lastLoadingStatistics.LevelsFound = tsData.Levels.Count;

                // Associate cables with tray segments
                var associations = AssociateCablesWithTraySegments(tsData.CableTraySegments, tsData.AllCables);
                _logger?.Log($"Created {associations} cable-tray associations", LogType.Information);

                // Load parametric cables
                if (options.LoadParametricCables)
                {
                    _lastLoadingStatistics.ParametricCablesLoaded = LoadParametricCables(tsData.CableTraySegments);
                }

                // Load section views
                if (options.LoadSectionViews)
                {
                    _lastLoadingStatistics.SectionViewsLoaded = LoadSectionViews(tsData.CableTraySegments);
                }

                // Initialize spatial indexes
                if (options.InitializeSpatialIndexes)
                {
                    InitializeSpatialIndexes(tsData);
                }

                // Validate data
                if (options.ValidateData)
                {
                    var validationIssues = ValidateLoadedData(tsData);
                    _lastLoadingStatistics.ValidationIssues = validationIssues.Count;
                    
                    if (validationIssues.Count > 0)
                    {
                        _logger?.Log($"Found {validationIssues.Count} validation issues", LogType.Warning);
                    }
                }

                // Cleanup orphaned references
                if (options.CleanupOrphanedReferences)
                {
                    _lastLoadingStatistics.OrphanedReferencesCleanedUp = CleanupOrphanedReferences(tsData);
                }

                _lastLoadingStatistics.CompleteLoading();
                _logger?.Log($"TS_Data loading completed: {_lastLoadingStatistics}", LogType.Information);

                return tsData;
            }
            catch (Exception ex)
            {
                _lastLoadingStatistics.CompleteLoading();
                _logger?.Log($"Failed to load TS_Data from document: {ex.Message}", LogType.Error);
                return new TS_Data(_document);
            }
        }

        /// <summary>
        /// Load existing cable tray segments with TS_Cables parameter data
        /// </summary>
        public List<CableTraySegmentModel> LoadExistingTraySegments()
        {
            var traySegments = new List<CableTraySegmentModel>();

            try
            {
                var cableTraysWithData = _trayAnalysisService.FindCableTraysWithCableData();
                if (_lastLoadingStatistics == null)
                {
                    MessageBox.Show("adffafdf");
                }
                else
                {
                    _lastLoadingStatistics.TotalCableTrays = _trayAnalysisService.FindAllCableTrays().Count;
                }
                

                foreach (var cableTray in cableTraysWithData)
                {
                    try
                    {
                        var traySegment = _trayAnalysisService.CreateTraySegmentModel(cableTray);
                        if (traySegment != null)
                        {
                            traySegments.Add(traySegment);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to create tray segment for {cableTray.Id}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Loaded {traySegments.Count} existing tray segments", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load existing tray segments: {ex.Message}", LogType.Error);
            }

            return traySegments;
        }

        /// <summary>
        /// Load all cables from electrical circuits
        /// </summary>
        public Dictionary<string, CableModel> LoadAllCablesFromCircuits()
        {
            try
            {
                var cables = _cableDetectionService.GetAllCablesFromCircuits();
                var circuits = _cableDetectionService.FindAllElectricalCircuits();
                if (_lastLoadingStatistics ==  null)
                {
                    MessageBox.Show("adffafdf");
                }
                else
                {
                    _lastLoadingStatistics.TotalCircuits = circuits.Count;
                }
                    

                _logger?.Log($"Loaded {cables.Count} cables from {circuits.Count} circuits", LogType.Information);
                return cables;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load cables from circuits: {ex.Message}", LogType.Error);
                return new Dictionary<string, CableModel>();
            }
        }

        /// <summary>
        /// Load electrical equipment from the document
        /// </summary>
        public Dictionary<ElementId, Element> LoadElectricalEquipment()
        {
            var equipment = new Dictionary<ElementId, Element>();

            try
            {
                var allEquipment = _cableDetectionService.FindAllElectricalEquipment();

                foreach (var element in allEquipment)
                {
                    equipment[element.Id] = element;
                }

                _logger?.Log($"Loaded {equipment.Count} electrical equipment elements", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load electrical equipment: {ex.Message}", LogType.Error);
            }

            return equipment;
        }

        /// <summary>
        /// Load levels from the document
        /// </summary>
        public Dictionary<ElementId, Level> LoadLevels()
        {
            var levels = new Dictionary<ElementId, Level>();

            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(Level))
                    .WhereElementIsNotElementType();

                foreach (Level level in collector)
                {
                    levels[level.Id] = level;
                }

                _logger?.Log($"Loaded {levels.Count} levels", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load levels: {ex.Message}", LogType.Error);
            }

            return levels;
        }

        #endregion

        #region Data Initialization

        /// <summary>
        /// Initialize spatial indexes for efficient queries
        /// </summary>
        public bool InitializeSpatialIndexes(TS_Data tsData)
        {
            try
            {
                if (tsData == null) return false;

                // Create spatial index for tray segments
                var trayIndex = _spatialIndexService.CreateTraySegmentIndex(tsData.CableTraySegments);
                
                // Create spatial index for cables
                var cableIndex = _spatialIndexService.CreateCableIndex(tsData.AllCables.Values.ToList());
                
                // Create spatial index for equipment
                var equipmentIndex = _spatialIndexService.CreateEquipmentIndex(tsData.ElectricalEquipment.Values.ToList());

                // Store indexes in TS_Data (simplified - in reality you'd have a proper container)
                tsData.SpatialIndex = new
                {
                    TrayIndex = trayIndex,
                    CableIndex = cableIndex,
                    EquipmentIndex = equipmentIndex
                };

                _logger?.Log("Spatial indexes initialized successfully", LogType.Information);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to initialize spatial indexes: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Associate cables with tray segments based on existing TS_Cables parameters
        /// </summary>
        public int AssociateCablesWithTraySegments(List<CableTraySegmentModel> traySegments, Dictionary<string, CableModel> availableCables)
        {
            int associationsCreated = 0;

            try
            {
                if (traySegments == null || availableCables == null) return 0;

                foreach (var traySegment in traySegments)
                {
                    try
                    {
                        var cableRefsParam = _parameterService.ReadTSCablesParameter(traySegment.CableTray);
                        if (!string.IsNullOrEmpty(cableRefsParam))
                        {
                            traySegment.SetCablesFromParameterValue(cableRefsParam, availableCables);
                            associationsCreated += traySegment.Cables.Count;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to associate cables for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Created {associationsCreated} cable-tray associations", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to associate cables with tray segments: {ex.Message}", LogType.Error);
            }

            return associationsCreated;
        }

        /// <summary>
        /// Load and associate parametric cables with tray segments
        /// </summary>
        public int LoadParametricCables(List<CableTraySegmentModel> traySegments)
        {
            int parametricCablesLoaded = 0;

            try
            {
                if (traySegments == null) return 0;

                foreach (var traySegment in traySegments)
                {
                    try
                    {
                        var parametricCableId = _parameterService.ReadTSParametricCableParameter(traySegment.CableTray);
                        if (parametricCableId != null)
                        {
                            var parametricCable = _document.GetElement(parametricCableId) as FamilyInstance;
                            if (parametricCable != null && parametricCable.IsValidObject)
                            {
                                traySegment.BecaParametricCable = parametricCable;
                                parametricCablesLoaded++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to load parametric cable for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Loaded {parametricCablesLoaded} parametric cables", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load parametric cables: {ex.Message}", LogType.Error);
            }

            return parametricCablesLoaded;
        }

        /// <summary>
        /// Load and associate section views with tray segments
        /// </summary>
        public int LoadSectionViews(List<CableTraySegmentModel> traySegments)
        {
            int sectionViewsLoaded = 0;

            try
            {
                if (traySegments == null) return 0;

                foreach (var traySegment in traySegments)
                {
                    try
                    {
                        var sectionViewId = _parameterService.ReadTSSectionViewParameter(traySegment.CableTray);
                        if (sectionViewId != null)
                        {
                            var sectionView = _document.GetElement(sectionViewId) as View;
                            if (sectionView != null && sectionView.IsValidObject)
                            {
                                traySegment.SectionView = sectionView;
                                sectionViewsLoaded++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to load section view for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Loaded {sectionViewsLoaded} section views", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load section views: {ex.Message}", LogType.Error);
            }

            return sectionViewsLoaded;
        }

        #endregion

        #region Data Validation

        /// <summary>
        /// Validate loaded TS_Data for consistency and integrity
        /// </summary>
        public List<string> ValidateLoadedData(TS_Data tsData)
        {
            var issues = new List<string>();

            try
            {
                if (tsData == null)
                {
                    issues.Add("TS_Data is null");
                    return issues;
                }

                // Validate tray segments
                foreach (var traySegment in tsData.CableTraySegments)
                {
                    var trayIssues = _trayAnalysisService.ValidateTraySegment(traySegment);
                    issues.AddRange(trayIssues);
                }

                // Validate cable references
                var cableValidationIssues = _cableDetectionService.CheckForDuplicateCableAssignments(tsData.CableTraySegments);
                issues.AddRange(cableValidationIssues);

                // Validate element references
                var referenceIssues = VerifyElementReferences(tsData);
                issues.AddRange(referenceIssues);

                // Validate data consistency
                issues.AddRange(tsData.ValidateData());

                _logger?.Log($"Data validation completed with {issues.Count} issues", LogType.Information);
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
                _logger?.Log($"Failed to validate loaded data: {ex.Message}", LogType.Error);
            }

            return issues;
        }

        /// <summary>
        /// Clean up orphaned references in loaded data
        /// </summary>
        public int CleanupOrphanedReferences(TS_Data tsData)
        {
            int cleanedUp = 0;

            try
            {
                if (tsData == null) return 0;

                // Clean up invalid parametric cable references
                foreach (var traySegment in tsData.CableTraySegments)
                {
                    if (traySegment.BecaParametricCable != null && !traySegment.BecaParametricCable.IsValidObject)
                    {
                        traySegment.BecaParametricCable = null;
                        cleanedUp++;
                    }

                    if (traySegment.SectionView != null && !traySegment.SectionView.IsValidObject)
                    {
                        traySegment.SectionView = null;
                        cleanedUp++;
                    }
                }

                // Clean up invalid equipment references
                var invalidEquipmentIds = tsData.ElectricalEquipment
                    .Where(kvp => kvp.Value == null || !kvp.Value.IsValidObject)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var invalidId in invalidEquipmentIds)
                {
                    tsData.ElectricalEquipment.Remove(invalidId);
                    cleanedUp++;
                }

                _logger?.Log($"Cleaned up {cleanedUp} orphaned references", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to cleanup orphaned references: {ex.Message}", LogType.Error);
            }

            return cleanedUp;
        }

        /// <summary>
        /// Verify that all referenced elements exist and are valid
        /// </summary>
        public List<string> VerifyElementReferences(TS_Data tsData)
        {
            var invalidReferences = new List<string>();

            try
            {
                if (tsData == null) return invalidReferences;

                // Verify tray segment references
                foreach (var traySegment in tsData.CableTraySegments)
                {
                    if (traySegment.CableTray == null || !traySegment.CableTray.IsValidObject)
                    {
                        invalidReferences.Add($"Tray segment {traySegment.TrayRef} has invalid cable tray reference");
                    }

                    // Verify cable equipment references
                    foreach (var cable in traySegment.Cables)
                    {
                        if (cable.From != null && !cable.From.IsValidObject)
                        {
                            invalidReferences.Add($"Cable {cable.CableRef} has invalid 'From' equipment reference");
                        }

                        if (cable.To != null && !cable.To.IsValidObject)
                        {
                            invalidReferences.Add($"Cable {cable.CableRef} has invalid 'To' equipment reference");
                        }
                    }
                }

                _logger?.Log($"Found {invalidReferences.Count} invalid element references", LogType.Information);
            }
            catch (Exception ex)
            {
                invalidReferences.Add($"Reference verification error: {ex.Message}");
                _logger?.Log($"Failed to verify element references: {ex.Message}", LogType.Error);
            }

            return invalidReferences;
        }

        #endregion

        #region Data Refresh

        /// <summary>
        /// Refresh TS_Data with latest information from document
        /// </summary>
        public bool RefreshTSData(TS_Data tsData)
        {
            try
            {
                if (tsData == null) return false;

                _logger?.Log("Refreshing TS_Data from document", LogType.Information);

                // Update cable data from circuits
                var updatedCables = UpdateCableDataFromCircuits(tsData);
                
                // Refresh tray segment data
                var traySegmentIds = tsData.CableTraySegments.Select(t => t.CableTray.Id).ToList();
                var refreshedTraySegments = ReloadTraySegments(traySegmentIds);

                // Update tray segments in TS_Data
                tsData.CableTraySegments.Clear();
                foreach (var traySegment in refreshedTraySegments)
                {
                    tsData.AddTraySegment(traySegment);
                }

                // Re-associate cables
                AssociateCablesWithTraySegments(tsData.CableTraySegments, tsData.AllCables);

                tsData.RefreshFromDocument();
                _logger?.Log($"TS_Data refresh completed - updated {updatedCables} cables", LogType.Information);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to refresh TS_Data: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Reload specific tray segments from document
        /// </summary>
        public List<CableTraySegmentModel> ReloadTraySegments(List<ElementId> traySegmentIds)
        {
            var reloadedSegments = new List<CableTraySegmentModel>();

            try
            {
                if (traySegmentIds == null) return reloadedSegments;

                foreach (var elementId in traySegmentIds)
                {
                    try
                    {
                        var cableTray = _document.GetElement(elementId) as CableTray;
                        if (cableTray != null && _trayAnalysisService.IsValidCableTray(cableTray))
                        {
                            var traySegment = _trayAnalysisService.CreateTraySegmentModel(cableTray);
                            if (traySegment != null)
                            {
                                reloadedSegments.Add(traySegment);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to reload tray segment {elementId}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Reloaded {reloadedSegments.Count} tray segments", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to reload tray segments: {ex.Message}", LogType.Error);
            }

            return reloadedSegments;
        }

        /// <summary>
        /// Update cable data from latest circuit information
        /// </summary>
        public int UpdateCableDataFromCircuits(TS_Data tsData)
        {
            int updatedCables = 0;

            try
            {
                if (tsData == null) return 0;

                var latestCables = LoadAllCablesFromCircuits();

                foreach (var latestCable in latestCables.Values)
                {
                    if (tsData.AllCables.ContainsKey(latestCable.CableRef))
                    {
                        // Update existing cable
                        tsData.AllCables[latestCable.CableRef] = latestCable;
                        updatedCables++;
                    }
                    else
                    {
                        // Add new cable
                        tsData.AddOrUpdateCable(latestCable);
                        updatedCables++;
                    }
                }

                _logger?.Log($"Updated {updatedCables} cables from circuit data", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update cable data from circuits: {ex.Message}", LogType.Error);
            }

            return updatedCables;
        }

        #endregion

        #region Performance Optimization

        /// <summary>
        /// Load data with progress reporting
        /// </summary>
        public TS_Data LoadTSDataWithProgress(Action<string, double> progressCallback)
        {
            try
            {
                var options = DataLoadingOptions.Complete;
                options.ReportProgress = true;

                progressCallback?.Invoke("Starting data load...", 0.0);

                var tsData = new TS_Data(_document);

                progressCallback?.Invoke("Loading cable trays...", 0.1);
                var traySegments = LoadExistingTraySegments();
                foreach (var traySegment in traySegments)
                {
                    tsData.AddTraySegment(traySegment);
                }

                progressCallback?.Invoke("Loading circuits and cables...", 0.3);
                var cables = LoadAllCablesFromCircuits();
                foreach (var cable in cables.Values)
                {
                    tsData.AddOrUpdateCable(cable);
                }

                progressCallback?.Invoke("Loading electrical equipment...", 0.5);
                tsData.ElectricalEquipment = LoadElectricalEquipment();

                progressCallback?.Invoke("Loading levels...", 0.6);
                tsData.Levels = LoadLevels();

                progressCallback?.Invoke("Associating cables with trays...", 0.7);
                AssociateCablesWithTraySegments(tsData.CableTraySegments, tsData.AllCables);

                progressCallback?.Invoke("Loading parametric cables and section views...", 0.8);
                LoadParametricCables(tsData.CableTraySegments);
                LoadSectionViews(tsData.CableTraySegments);

                progressCallback?.Invoke("Initializing spatial indexes...", 0.9);
                InitializeSpatialIndexes(tsData);

                progressCallback?.Invoke("Validating data...", 0.95);
                ValidateLoadedData(tsData);
                CleanupOrphanedReferences(tsData);

                progressCallback?.Invoke("Data loading complete!", 1.0);

                return tsData;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke($"Error: {ex.Message}", 1.0);
                _logger?.Log($"Failed to load TS_Data with progress: {ex.Message}", LogType.Error);
                return new TS_Data(_document);
            }
        }

        /// <summary>
        /// Load data incrementally for large documents
        /// </summary>
        public TS_Data LoadTSDataIncrementally(int batchSize = 100)
        {
            try
            {
                _logger?.Log($"Loading TS_Data incrementally with batch size {batchSize}", LogType.Information);

                var tsData = new TS_Data(_document);

                // Load in batches to avoid memory issues
                var allTrays = _trayAnalysisService.FindAllCableTrays();
                var traysWithData = _trayAnalysisService.FindCableTraysWithCableData();

                for (int i = 0; i < traysWithData.Count; i += batchSize)
                {
                    var batch = traysWithData.Skip(i).Take(batchSize).ToList();
                    
                    foreach (var cableTray in batch)
                    {
                        var traySegment = _trayAnalysisService.CreateTraySegmentModel(cableTray);
                        if (traySegment != null)
                        {
                            tsData.AddTraySegment(traySegment);
                        }
                    }

                    _logger?.Log($"Processed batch {i / batchSize + 1} of {(traysWithData.Count + batchSize - 1) / batchSize}", LogType.Information);
                }

                // Load cables and other data normally
                var cables = LoadAllCablesFromCircuits();
                foreach (var cable in cables.Values)
                {
                    tsData.AddOrUpdateCable(cable);
                }

                tsData.ElectricalEquipment = LoadElectricalEquipment();
                tsData.Levels = LoadLevels();

                AssociateCablesWithTraySegments(tsData.CableTraySegments, tsData.AllCables);
                LoadParametricCables(tsData.CableTraySegments);
                LoadSectionViews(tsData.CableTraySegments);

                _logger?.Log("Incremental data loading completed", LogType.Information);
                return tsData;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load TS_Data incrementally: {ex.Message}", LogType.Error);
                return new TS_Data(_document);
            }
        }

        /// <summary>
        /// Check if document has changed since last load
        /// </summary>
        public bool HasDocumentChanged(DateTime lastLoadTime)
        {
            try
            {
                // This is a simplified implementation
                // In reality, you would check document modification timestamps or use Revit's change tracking
                return DateTime.Now - lastLoadTime > TimeSpan.FromMinutes(1);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check document changes: {ex.Message}", LogType.Error);
                return true; // Assume changed if we can't determine
            }
        }

        #endregion

        #region Statistics and Reporting

        /// <summary>
        /// Get loading statistics for the last load operation
        /// </summary>
        public DataLoadingStatistics GetLoadingStatistics()
        {
            return _lastLoadingStatistics ?? new DataLoadingStatistics();
        }

        /// <summary>
        /// Generate loading report with details about loaded data
        /// </summary>
        public string GenerateLoadingReport(TS_Data tsData)
        {
            try
            {
                if (tsData == null) return "No data loaded";

                var report = new System.Text.StringBuilder();
                var stats = GetLoadingStatistics();

                report.AppendLine("=== Traylor Swift Data Loading Report ===");
                report.AppendLine($"Document: {stats.DocumentTitle}");
                report.AppendLine($"Loading Time: {stats.LoadingTimeSeconds:F1} seconds");
                report.AppendLine($"Loading Date: {stats.LoadingEndTime:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                report.AppendLine("=== Data Summary ===");
                report.AppendLine($"Total Cable Trays: {stats.TotalCableTrays}");
                report.AppendLine($"Tray Segments with Cable Data: {stats.TraySegmentsWithCableData}");
                report.AppendLine($"Total Circuits: {stats.TotalCircuits}");
                report.AppendLine($"Cables Created: {stats.CablesCreated}");
                report.AppendLine($"Electrical Equipment: {stats.ElectricalEquipmentFound}");
                report.AppendLine($"Levels: {stats.LevelsFound}");
                report.AppendLine($"Parametric Cables: {stats.ParametricCablesLoaded}");
                report.AppendLine($"Section Views: {stats.SectionViewsLoaded}");
                report.AppendLine();

                report.AppendLine("=== Quality Metrics ===");
                report.AppendLine($"Validation Issues: {stats.ValidationIssues}");
                report.AppendLine($"Orphaned References Cleaned: {stats.OrphanedReferencesCleanedUp}");
                report.AppendLine();

                report.AppendLine("=== TS_Data Statistics ===");
                report.AppendLine(tsData.Statistics.ToString());

                return report.ToString();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to generate loading report: {ex.Message}", LogType.Error);
                return $"Error generating report: {ex.Message}";
            }
        }

        #endregion
    }
}
