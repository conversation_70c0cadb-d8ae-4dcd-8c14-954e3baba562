﻿<Window
    x:Class="MEP.TraylorSwift.Views.ModelessMainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.TraylorSwift.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.TraylorSwift.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:MEP.TraylorSwift.ViewModels"
    Title="Traylor Swift - Cable Tray Tagging"
    Width="1200"
    Height="800"
    MinWidth="800"
    MinHeight="600"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
            <converters:DoubleFormatConverter x:Key="DoubleFormatConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:CountToStringConverter x:Key="CountToStringConverter" />

        </ResourceDictionary>
    </Window.Resources>
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <!--<Image
                    Width="40"
                    Height="40"
                    Margin="10,-5,0,0"
                    Source="/MEP.TraylorSwift;component/Views/TraylorSwift.png" />-->
                <TextBlock
                    Margin="15,5,0,10"
                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                    Text="TRAYLOR SWIFT" />
            </StackPanel>

            <Image
                Grid.Column="1"
                Width="50"
                Height="50"
                Margin="10,0,20,20"
                Source="/MEP.TraylorSwift;component/Views/TraylorSwift.png" />
            <!--<Button
                Grid.Column="1"
                Height="45"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Background="Transparent"
                BorderBrush="Transparent"
                Command="{Binding OpenDocumentationCommand}"
                Content="{materialDesign:PackIcon Kind=HelpCircleOutline, Size=38}"
                Foreground="#12A8B2" />-->
        </Grid>

        <Separator
            Grid.Row="0"
            Margin="10,45,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Main Content  -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Left Panel - Controls  -->
            <Border
                Grid.Column="0"
                Padding="10"
                Background="{DynamicResource MaterialDesignPaper}"
                CornerRadius="4">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!--  Data Loading Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Data Management" />

                                <Button
                                    Margin="0,5,0,2"
                                    Command="{Binding LoadDataCommand}"
                                    Content="Load Data"
                                    IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,2"
                                    Command="{Binding RefreshDataCommand}"
                                    Content="Refresh Data"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />

                                <Button
                                    Margin="0,2"
                                    Command="{Binding ClearDataCommand}"
                                    Content="Clear Data"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />

                                <Button
                                    Margin="0,2,0,0"
                                    Command="{Binding ShowStatisticsCommand}"
                                    Content="Show Statistics"
                                    Style="{StaticResource MaterialDesignFlatButton}" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Level Filter Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Level Filter" />

                                <ComboBox
                                    Margin="0,5,0,0"
                                    materialDesign:HintAssist.Hint="Select Level"
                                    ItemsSource="{Binding AvailableLevels}"
                                    SelectedItem="{Binding SelectedLevel}" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Tray Operations Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Tray Operations" />

                                <Button
                                    Margin="0,5,0,2"
                                    Command="{Binding AddTraySegmentCommand}"
                                    Content="Add Tray Segment"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,2"
                                    Command="{Binding DetectCablesCommand}"
                                    Content="Detect Cables"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,2"
                                    Command="{Binding RemoveTraySegmentCommand}"
                                    Content="Remove Tray"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Output Generation Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Output Generation" />

                                <Button
                                    Margin="0,5,0,2"
                                    Command="{Binding CreateTagsCommand}"
                                    Content="Create Tags"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,2"
                                    Command="{Binding CreateSectionViewCommand}"
                                    Content="Create Section View"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />

                                <Button
                                    Margin="0,2,0,0"
                                    Command="{Binding CreateParametricCableCommand}"
                                    Content="Create Parametric Cable"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Cable Viewer Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Text="Cable Management" />

                                <Button
                                    Margin="0,5,0,2"
                                    Command="{Binding OpenCableViewerCommand}"
                                    Content="Cable Viewer"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!--  Splitter  -->
            <GridSplitter
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                Background="{DynamicResource MaterialDesignDivider}" />

            <!--  Right Panel - Page Navigation  -->
            <Grid Grid.Column="2" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Navigation Header  -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,10" Padding="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="{Binding CurrentPageTitle}" />

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button
                                Margin="0,0,5,0"
                                Command="{Binding NavigateToTraySegmentsCommand}"
                                Content="Tray Segments"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                ToolTip="View all cable tray segments" />
                            <Button
                                Command="{Binding OpenCableViewerCommand}"
                                Content="Cable Viewer"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                ToolTip="View all cables in the project" />
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!--  Page Content Frame  -->
                <Frame
                    x:Name="MainContentFrame"
                    Grid.Row="1"
                    NavigationUIVisibility="Hidden" />
            </Grid>
        </Grid>

        <!--  Progress Bar  -->
        <ProgressBar
            Grid.Row="2"
            Margin="0,5,0,5"
            IsIndeterminate="{Binding IsLoading}"
            Visibility="{Binding ShowProgressBar, Converter={StaticResource BooleanToVisibilityConverter}}"
            Value="{Binding ProgressValue}" />

        <!--  Status Bar  -->
        <Border
            Grid.Row="3"
            Padding="10,5"
            Background="{DynamicResource MaterialDesignDivider}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage}" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Image
                        Height="25"
                        Margin="0,0,10,0"
                        Source="/MEP.TraylorSwift;component/Views/BecaLogoBlack.png" />
                    <TextBlock
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignCaptionTextBlock}"
                        Text="Make Everyday Better" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
