using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.TraylorSwift.Handlers;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services;
using MEP.TraylorSwift.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;

namespace MEP.TraylorSwift.ViewModels
{
    /// <summary>
    /// Main ViewModel for the Traylor Swift cable tray tagging workflow
    /// </summary>
    public partial class TrayTaggerViewModel : BaseViewModel
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private readonly IDataLoadingService _dataLoadingService;
        private readonly ITrayAnalysisService _trayAnalysisService;
        private readonly ICableDetectionService _cableDetectionService;
        private readonly ITaggingService _taggingService;
        private readonly ISectionViewService _sectionViewService;
        private readonly IParametricCableService _parametricCableService;
        private readonly NavigationService _navigationService;

        [ObservableProperty]
        private TS_Data _tSData;

        [ObservableProperty]
        private string _selectedLevel = "All";

        [ObservableProperty]
        private CableTraySegmentModel _selectedTraySegment;

        [ObservableProperty]
        private double _progressValue;

        [ObservableProperty]
        private bool _showProgressBar;

        [ObservableProperty]
        private string _currentPageTitle = "Cable Tray Segments";

        #endregion

        #region Properties

        /// <summary>
        /// Collection of cable tray segments for display
        /// </summary>
        public ObservableCollection<CableTraySegmentModel> TraySegments { get; private set; }

        /// <summary>
        /// Filtered view of tray segments based on selected level
        /// </summary>
        public ICollectionView TraySegmentsView { get; private set; }

        /// <summary>
        /// Available levels for filtering
        /// </summary>
        public ObservableCollection<string> AvailableLevels { get; private set; }

        /// <summary>
        /// Navigation service for page management
        /// </summary>
        public NavigationService NavigationService => _navigationService;

        /// <summary>
        /// Initialize navigation and navigate to default page
        /// </summary>
        public void InitializeNavigation(System.Windows.Controls.Frame frame)
        {
            try
            {
                if (_navigationService != null && frame != null)
                {
                    _navigationService.Initialize(frame);
                    NavigateToTraySegments();
                    System.Diagnostics.Debug.WriteLine("Navigation initialized from ViewModel");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ViewModel InitializeNavigation: {ex.Message}");
                _logger?.Log($"Failed to initialize navigation: {ex.Message}", LogType.Error);
            }
        }

        // Properties auto-generated by [ObservableProperty] attributes

        // Custom property change handlers for MVVM Toolkit
        partial void OnSelectedLevelChanged(string value)
        {
            FilterTraySegmentsByLevel();
        }

        partial void OnSelectedTraySegmentChanged(CableTraySegmentModel value)
        {
            OnSelectedTraySegmentChangedInternal();
        }

        /// <summary>
        /// Statistics about the loaded data
        /// </summary>
        public TS_DataStatistics DataStatistics => TSData?.Statistics;

        #endregion

        // Commands auto-generated by [RelayCommand] attributes on methods below

        #region Constructor

        /// <summary>
        /// Initialize the TrayTaggerViewModel
        /// </summary>
        public TrayTaggerViewModel(
            Document document,
            BecaActivityLoggerData logger,
            IDataLoadingService dataLoadingService,
            ITrayAnalysisService trayAnalysisService,
            ICableDetectionService cableDetectionService,
            ITaggingService taggingService,
            ISectionViewService sectionViewService,
            IParametricCableService parametricCableService,
            IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
            _dataLoadingService = dataLoadingService ?? throw new ArgumentNullException(nameof(dataLoadingService));
            _trayAnalysisService = trayAnalysisService ?? throw new ArgumentNullException(nameof(trayAnalysisService));
            _cableDetectionService = cableDetectionService ?? throw new ArgumentNullException(nameof(cableDetectionService));
            _taggingService = taggingService ?? throw new ArgumentNullException(nameof(taggingService));
            _sectionViewService = sectionViewService ?? throw new ArgumentNullException(nameof(sectionViewService));
            _parametricCableService = parametricCableService ?? throw new ArgumentNullException(nameof(parametricCableService));
            _navigationService = new NavigationService();

            // Initialize collections
            TraySegments = new ObservableCollection<CableTraySegmentModel>();
            AvailableLevels = new ObservableCollection<string>();

            // Create filtered view
            TraySegmentsView = CollectionViewSource.GetDefaultView(TraySegments);
            TraySegmentsView.Filter = FilterTraySegment;

            // Commands will be auto-generated by [RelayCommand] attributes

            // Initialize with empty data
            TSData = new TS_Data(_document);
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load data from the Revit document
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanLoadData))]
        private void LoadData()
        {
            try
            {
                IsLoading = true;
                ShowProgressBar = true;
                StatusMessage = "Loading data from document...";
                ProgressValue = 0;

                TSData = _dataLoadingService.LoadTSDataWithProgress((message, progress) =>
                {
                    StatusMessage = message;
                    ProgressValue = progress * 100;
                });

                // Update UI collections
                UpdateTraySegments();
                UpdateAvailableLevels();

                var stats = _dataLoadingService.GetLoadingStatistics();
                StatusMessage = $"Loaded {stats.TraySegmentsWithCableData} tray segments, {stats.CablesCreated} cables";

                _logger?.Log($"Data loading completed: {stats}", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading data: {ex.Message}";
                _logger?.Log($"Failed to load data: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
                ShowProgressBar = false;
                ProgressValue = 0;
            }
        }

        private bool CanLoadData() => !IsLoading;

        /// <summary>
        /// Refresh data from the Revit document
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRefreshData))]
        private void RefreshData()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Refreshing data...";

                _dataLoadingService.RefreshTSData(TSData);

                UpdateTraySegments();
                UpdateAvailableLevels();

                StatusMessage = "Data refreshed successfully";
                _logger?.Log("Data refresh completed", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing data: {ex.Message}";
                _logger?.Log($"Failed to refresh data: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanRefreshData() => !IsLoading && TSData != null;

        #endregion

        #region Tray Segment Management

        /// <summary>
        /// Add a new tray segment (request user selection in Revit)
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanAddTraySegment))]
        private void AddTraySegment()
        {
            try
            {
                StatusMessage = "Requesting tray selection in Revit...";

                // Request external event for tray selection
                MakeRequest(RequestId.AddTraySegment);

                _logger?.Log("Add tray segment requested - user selection required", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error adding tray segment: {ex.Message}";
                _logger?.Log($"Failed to add tray segment: {ex.Message}", LogType.Error);
            }
        }

        private bool CanAddTraySegment() => !IsLoading;

        /// <summary>
        /// Remove a tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRemoveTraySegment))]
        private void RemoveTraySegment(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return;

                StatusMessage = $"Removing tray segment {traySegment.TrayRef}...";

                // Store tray segment for processing
                SharedData.SelectedTraySegmentForProcessing = traySegment;
                SharedData.ProcessingAction = "RemoveTraySegment";

                // Request external event
                MakeRequest(RequestId.RemoveTraySegment);

                // Remove from UI collection
                TraySegments.Remove(traySegment);
                if (TSData?.CableTraySegments != null)
                {
                    TSData.CableTraySegments.Remove(traySegment);
                }

                StatusMessage = $"Tray segment {traySegment.TrayRef} removed.";
                _logger?.Log($"Tray segment {traySegment.TrayRef} removed", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error removing tray segment: {ex.Message}";
                _logger?.Log($"Failed to remove tray segment: {ex.Message}", LogType.Error);
            }
        }

        private bool CanRemoveTraySegment(CableTraySegmentModel traySegment) => !IsLoading && traySegment != null;

        /// <summary>
        /// Configure a tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanConfigureTraySegment))]
        private void ConfigureTraySegment(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return;

                // Create configure page view model
                var sectionViewService = _serviceProvider.GetService<ISectionViewService>();
                var parametricCableService = _serviceProvider.GetService<IParametricCableService>();
                var trayAnalysisService = _serviceProvider.GetService<ITrayAnalysisService>();

                var configureViewModel = new ConfigureTraySegmentViewModel(
                    traySegment,
                    sectionViewService,
                    parametricCableService,
                    trayAnalysisService,
                    _serviceProvider);

                // Initialize the configure view model with the same RequestHandler and ExternalEvent
                configureViewModel.Initialize(_requestHandler, _externalEvent);

                // Subscribe to close request to navigate back
                configureViewModel.CloseRequested += (s, e) => NavigateToTraySegments();

                // Create and navigate to configure page
                var configurePage = new Views.Pages.ConfigureTraySegmentPage();
                configurePage.DataContext = configureViewModel;

                _navigationService.NavigateTo(configurePage);
                CurrentPageTitle = $"Configure: {traySegment.TrayRef}";

                StatusMessage = $"Configuring tray segment: {traySegment.TrayRef}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error configuring tray segment: {ex.Message}";
                _logger?.Log($"Failed to configure tray segment: {ex.Message}", LogType.Error);
            }
        }

        private bool CanConfigureTraySegment(CableTraySegmentModel traySegment) => !IsLoading && traySegment != null;

        /// <summary>
        /// Open cable viewer window
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanOpenCableViewer))]
        private void OpenCableViewer()
        {
            try
            {
                if (TSData == null) return;

                // Create cable viewer page view model
                var cableViewerViewModel = new CableViewerViewModel(TSData, _serviceProvider);

                // Initialize with external event handling if needed
                cableViewerViewModel.Initialize(_requestHandler, _externalEvent);

                // Subscribe to close request to navigate back
                cableViewerViewModel.CloseRequested += (s, e) => NavigateToTraySegments();

                // Create and navigate to cable viewer page
                var cableViewerPage = new Views.Pages.CableViewerPage();
                cableViewerPage.DataContext = cableViewerViewModel;

                _navigationService.NavigateTo(cableViewerPage);
                CurrentPageTitle = "Cable Viewer";

                StatusMessage = "Cable viewer opened.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error opening cable viewer: {ex.Message}";
                _logger?.Log($"Failed to open cable viewer: {ex.Message}", LogType.Error);
            }
        }

        private bool CanOpenCableViewer() => !IsLoading && TSData != null;

        /// <summary>
        /// Navigate to tray segments page
        /// </summary>
        [RelayCommand]
        private void NavigateToTraySegments()
        {
            try
            {
                // Create and navigate to tray segments page
                var traySegmentsPage = new Views.Pages.TraySegmentsPage();
                traySegmentsPage.DataContext = this;

                _navigationService.NavigateTo(traySegmentsPage);
                CurrentPageTitle = "Cable Tray Segments";

                StatusMessage = "Navigated to tray segments.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error navigating to tray segments: {ex.Message}";
                _logger?.Log($"Failed to navigate to tray segments: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Cable Detection

        /// <summary>
        /// Detect cables for the selected tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanDetectCables))]
        private void DetectCables()
        {
            try
            {
                if (SelectedTraySegment?.CableTray == null) return;

                IsLoading = true;
                StatusMessage = "Detecting cables...";

                var options = new Models.CableDetectionOptions
                {
                    UseGeometricIntersection = true,
                    UseCircuitPathAnalysis = true,
                    UseProximityAnalysis = true,
                    MinimumConfidenceScore = 0.5
                };

                var circuits = _cableDetectionService.FindAllElectricalCircuits();
                var detectionResults = _cableDetectionService.PerformMultiCriteriaCableDetection(
                    SelectedTraySegment.CableTray,
                    TSData.AllCables,
                    circuits,
                    options);

                // Update tray segment with detected cables
                SelectedTraySegment.Cables.Clear();
                foreach (var result in detectionResults)
                {
                    SelectedTraySegment.Cables.Add(result.Cable);
                    result.Cable.AssociatedTraySegment = SelectedTraySegment;
                }

                // Update TS_Cables parameter
                var cableRefs = string.Join(",", detectionResults.Select(r => r.Cable.CableRef));
                _trayAnalysisService.WriteTSCablesParameter(SelectedTraySegment.CableTray, cableRefs);

                StatusMessage = $"Detected {SelectedTraySegment.Cables.Count} cables for tray {SelectedTraySegment.TrayRef}";
                _logger?.Log($"Cable detection completed for tray {SelectedTraySegment.TrayRef}", LogType.Information);

                // Refresh the selected tray segment to update UI
                OnPropertyChanged(nameof(SelectedTraySegment));
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error detecting cables: {ex.Message}";
                _logger?.Log($"Failed to detect cables: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanDetectCables() => !IsLoading && SelectedTraySegment != null;

        #endregion

        #region Tag and View Creation

        /// <summary>
        /// Create tags for the selected tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanCreateTags))]
        private void CreateTags()
        {
            try
            {
                if (SelectedTraySegment?.CableTray == null) return;

                IsLoading = true;
                StatusMessage = "Creating tags...";

                var tag = _taggingService.CreateTrayTag(SelectedTraySegment.CableTray, SelectedTraySegment);
                if (tag != null)
                {
                    StatusMessage = $"Created tag for tray {SelectedTraySegment.TrayRef}";
                    _logger?.Log($"Tag created for tray {SelectedTraySegment.TrayRef}", LogType.Information);
                }
                else
                {
                    StatusMessage = $"Failed to create tag for tray {SelectedTraySegment.TrayRef}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error creating tags: {ex.Message}";
                _logger?.Log($"Failed to create tags: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanCreateTags() => !IsLoading && SelectedTraySegment != null;

        /// <summary>
        /// Create section view for the selected tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanCreateSectionView))]
        private void CreateSectionView()
        {
            try
            {
                if (SelectedTraySegment == null) return;

                IsLoading = true;
                StatusMessage = "Creating section view...";

                var sectionName = $"Section - {SelectedTraySegment.TrayRef}";
                var sectionView = _sectionViewService.CreateTraySectionView(SelectedTraySegment, sectionName);

                if (sectionView != null)
                {
                    SelectedTraySegment.SectionView = sectionView;
                    _trayAnalysisService.WriteTSSectionViewParameter(SelectedTraySegment.CableTray, sectionView.Id);

                    StatusMessage = $"Created section view for tray {SelectedTraySegment.TrayRef}";
                    _logger?.Log($"Section view created for tray {SelectedTraySegment.TrayRef}", LogType.Information);
                }
                else
                {
                    StatusMessage = $"Failed to create section view for tray {SelectedTraySegment.TrayRef}";
                }

                // Refresh the selected tray segment to update UI
                OnPropertyChanged(nameof(SelectedTraySegment));
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error creating section view: {ex.Message}";
                _logger?.Log($"Failed to create section view: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanCreateSectionView() => !IsLoading && SelectedTraySegment != null;

        /// <summary>
        /// Create parametric cable for the selected tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanCreateParametricCable))]
        private void CreateParametricCable()
        {
            try
            {
                if (SelectedTraySegment?.CableTray == null) return;

                IsLoading = true;
                StatusMessage = "Creating parametric cable...";

                var parametricCable = _parametricCableService.CreateParametricCable(SelectedTraySegment.CableTray, SelectedTraySegment);

                if (parametricCable != null)
                {
                    SelectedTraySegment.BecaParametricCable = parametricCable;
                    _trayAnalysisService.WriteTSParametricCableParameter(SelectedTraySegment.CableTray, parametricCable.Id);

                    StatusMessage = $"Created parametric cable for tray {SelectedTraySegment.TrayRef}";
                    _logger?.Log($"Parametric cable created for tray {SelectedTraySegment.TrayRef}", LogType.Information);
                }
                else
                {
                    StatusMessage = $"Failed to create parametric cable for tray {SelectedTraySegment.TrayRef}";
                }

                // Refresh the selected tray segment to update UI
                OnPropertyChanged(nameof(SelectedTraySegment));
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error creating parametric cable: {ex.Message}";
                _logger?.Log($"Failed to create parametric cable: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanCreateParametricCable() => !IsLoading && SelectedTraySegment != null;

        #endregion

        #region Utility Methods

        /// <summary>
        /// Update the tray segments collection from TS_Data
        /// </summary>
        private void UpdateTraySegments()
        {
            TraySegments.Clear();
            if (TSData?.CableTraySegments != null)
            {
                foreach (var segment in TSData.CableTraySegments)
                {
                    TraySegments.Add(segment);
                }
            }
            TraySegmentsView.Refresh();
        }

        /// <summary>
        /// Update the available levels collection
        /// </summary>
        private void UpdateAvailableLevels()
        {
            AvailableLevels.Clear();
            if (TSData != null)
            {
                var levelNames = TSData.GetUniqueLevelNames();
                foreach (var levelName in levelNames)
                {
                    AvailableLevels.Add(levelName);
                }
            }
        }

        /// <summary>
        /// Filter tray segments by selected level
        /// </summary>
        private void FilterTraySegmentsByLevel()
        {
            TraySegmentsView.Refresh();
        }

        /// <summary>
        /// Filter predicate for tray segments
        /// </summary>
        private bool FilterTraySegment(object item)
        {
            if (item is CableTraySegmentModel traySegment)
            {
                if (SelectedLevel == "All") return true;
                return string.Equals(traySegment.GetLevelName(), SelectedLevel, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Handle selected tray segment changed
        /// </summary>
        private void OnSelectedTraySegmentChangedInternal()
        {
            // Update command can execute states
            CommandManager.InvalidateRequerySuggested();

            if (SelectedTraySegment != null)
            {
                StatusMessage = $"Selected tray: {SelectedTraySegment.TrayRef} ({SelectedTraySegment.Cables.Count} cables)";
            }
            else
            {
                StatusMessage = "No tray selected";
            }
        }

        /// <summary>
        /// Clear all data
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanClearData))]
        private void ClearData()
        {
            try
            {
                TSData?.Clear();
                TraySegments.Clear();
                AvailableLevels.Clear();
                SelectedTraySegment = null;
                SelectedLevel = "All";
                StatusMessage = "Data cleared";

                _logger?.Log("Data cleared", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error clearing data: {ex.Message}";
                _logger?.Log($"Failed to clear data: {ex.Message}", LogType.Error);
            }
        }

        private bool CanClearData() => !IsLoading && TSData != null;

        /// <summary>
        /// Show data statistics
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanShowStatistics))]
        private void ShowStatistics()
        {
            try
            {
                if (TSData?.Statistics != null)
                {
                    var stats = TSData.Statistics;
                    var message = $"Statistics:\n" +
                                $"Tray Segments: {stats.TotalTraySegments}\n" +
                                $"Cables: {stats.TotalCables}\n" +
                                $"Levels: {stats.TotalLevels}\n" +
                                $"Average Capacity: {stats.AverageCapacity:F1}%\n" +
                                $"Last Updated: {stats.LastUpdated:yyyy-MM-dd HH:mm:ss}";

                    StatusMessage = "Statistics displayed";
                    _logger?.Log($"Statistics: {stats}", LogType.Information);

                    // In a real implementation, you would show this in a dialog
                    System.Windows.MessageBox.Show(message, "Traylor Swift Statistics",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error showing statistics: {ex.Message}";
                _logger?.Log($"Failed to show statistics: {ex.Message}", LogType.Error);
            }
        }

        private bool CanShowStatistics() => TSData != null;

        #endregion

        #region External Event Response Handling

        /// <summary>
        /// Override WakeUp to process SharedData after external events
        /// </summary>
        public override void WakeUp()
        {
            base.WakeUp();

            try
            {
                // Process SharedData based on the action performed
                if (!string.IsNullOrEmpty(SharedData.ProcessingAction))
                {
                    switch (SharedData.ProcessingAction)
                    {
                        case "AddTraySegment":
                            ProcessAddTraySegmentResponse();
                            break;
                        case "RemoveTraySegment":
                            ProcessRemoveTraySegmentResponse();
                            break;
                        case "WriteTSCablesParameter":
                            ProcessWriteTSCablesParameterResponse();
                            break;
                    }

                    // Clear processing action
                    SharedData.ProcessingAction = null;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing external event response: {ex.Message}";
                _logger?.Log($"Failed to process external event response: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Process the response from AddTraySegment external event
        /// </summary>
        private void ProcessAddTraySegmentResponse()
        {
            try
            {
                var selectedTray = SharedData.SelectedTrayForProcessing;
                if (selectedTray == null)
                {
                    StatusMessage = "No tray was selected";
                    _logger?.Log("ProcessAddTraySegmentResponse called but no tray was selected", LogType.Warning);
                    return;
                }

                // Validate tray element is still valid
                if (!selectedTray.IsValidObject)
                {
                    StatusMessage = "Selected tray is no longer valid in the document";
                    _logger?.Log($"Selected tray {selectedTray.Id} is no longer valid", LogType.Error);
                    return;
                }

                // Ensure TSData is initialized
                if (TSData == null)
                {
                    StatusMessage = "Data not loaded. Please click 'Load Data' first.";
                    _logger?.Log("TSData is null when trying to add tray segment", LogType.Error);
                    return;
                }

                // Check if tray already exists in the collection
                var existingSegment = TraySegments?.FirstOrDefault(ts => ts.CableTray?.Id == selectedTray.Id);
                if (existingSegment != null)
                {
                    StatusMessage = $"Tray {existingSegment.TrayRef} is already in the collection";
                    SelectedTraySegment = existingSegment;
                    _logger?.Log($"Tray {existingSegment.TrayRef} already exists in collection", LogType.Information);
                    return;
                }

                // Create new tray segment model
                var newSegment = CreateTraySegmentModel(selectedTray);
                if (newSegment != null)
                {
                    // Validate the new segment
                    if (string.IsNullOrEmpty(newSegment.TrayRef))
                    {
                        newSegment.TrayRef = $"Tray_{selectedTray.Id}";
                        _logger?.Log($"Generated TrayRef for tray {selectedTray.Id}", LogType.Information);
                    }

                    // Add to collections with validation
                    TraySegments.Add(newSegment);
                    TSData.AddTraySegment(newSegment);

                    // Verify the segment was added successfully
                    var addedToUI = TraySegments.Contains(newSegment);
                    var addedToData = TSData.CableTraySegments.Contains(newSegment);

                    if (!addedToUI || !addedToData)
                    {
                        StatusMessage = $"Warning: Tray segment may not have been added correctly (UI: {addedToUI}, Data: {addedToData})";
                        _logger?.Log($"Tray segment addition validation failed - UI: {addedToUI}, Data: {addedToData}", LogType.Warning);
                    }

                    // Update available levels
                    UpdateAvailableLevels();

                    // Refresh the filtered view to show the new item
                    TraySegmentsView.Refresh();

                    // Select the new segment
                    SelectedTraySegment = newSegment;

                    // Write TS_Cables parameter to persist the tray segment
                    // This ensures the tray will be found on subsequent loads
                    WriteInitialTSCablesParameter(newSegment);

                    SelectedLevel = newSegment.GetLevelName();

                    StatusMessage = $"Added tray segment: {newSegment.TrayRef}";
                    _logger?.Log($"Tray segment added successfully: {newSegment.TrayRef} (Total segments: {TraySegments.Count})", LogType.Information);
                }
                else
                {
                    StatusMessage = "Failed to create tray segment model";
                    _logger?.Log($"Failed to create tray segment model for tray {selectedTray.Id}", LogType.Error);
                }

                // Clear shared data
                SharedData.SelectedTrayForProcessing = null;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing added tray segment: {ex.Message}";
                _logger?.Log($"Failed to process added tray segment: {ex.Message}", LogType.Error);

                // Clear shared data even on error
                SharedData.SelectedTrayForProcessing = null;
            }
        }

        /// <summary>
        /// Process the response from RemoveTraySegment external event
        /// </summary>
        private void ProcessRemoveTraySegmentResponse()
        {
            try
            {
                var segmentToRemove = SharedData.SelectedTraySegmentForProcessing;
                if (segmentToRemove != null)
                {
                    // Remove from collections with validation
                    var removedFromUI = TraySegments.Remove(segmentToRemove);
                    var removedFromData = TSData?.CableTraySegments?.Remove(segmentToRemove) ?? false;

                    if (removedFromUI || removedFromData)
                    {
                        // Update available levels
                        UpdateAvailableLevels();

                        // Refresh the filtered view
                        TraySegmentsView.Refresh();

                        StatusMessage = $"Removed tray segment: {segmentToRemove.TrayRef}";
                        _logger?.Log($"Tray segment removed successfully: {segmentToRemove.TrayRef} (UI: {removedFromUI}, Data: {removedFromData})", LogType.Information);
                    }
                    else
                    {
                        StatusMessage = $"Tray segment {segmentToRemove.TrayRef} was not found in collections";
                        _logger?.Log($"Tray segment {segmentToRemove.TrayRef} was not found in collections", LogType.Warning);
                    }
                }

                // Clear shared data
                SharedData.SelectedTraySegmentForProcessing = null;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing removed tray segment: {ex.Message}";
                _logger?.Log($"Failed to process removed tray segment: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Process the response from WriteTSCablesParameter external event
        /// </summary>
        private void ProcessWriteTSCablesParameterResponse()
        {
            try
            {
                // The parameter writing is handled in the RequestHandler
                // This method is called after the parameter has been written
                // We can update the UI status or perform any follow-up actions

                StatusMessage = "Tray segment parameter updated successfully";
                _logger?.Log("TS_Cables parameter written successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing parameter write response: {ex.Message}";
                _logger?.Log($"Failed to process parameter write response: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Create a CableTraySegmentModel from a FamilyInstance
        /// </summary>
        private CableTraySegmentModel CreateTraySegmentModel(CableTray cableTray)
        {
            try
            {
                var segment = new CableTraySegmentModel
                {
                    CableTray = cableTray,
                    TrayRef = cableTray.get_Parameter(BuiltInParameter.ALL_MODEL_MARK)?.AsString() ?? cableTray.Id.ToString(),
                    Level = _document.GetElement(cableTray.LevelId) as Level,
                    Width = GetTrayWidth(cableTray),
                    Cables = new ObservableCollection<CableModel>()
                };

                // Try to get existing cable data from TS_Cables parameter
                var tsCablesParam = cableTray.LookupParameter("TS_Cables");
                if (tsCablesParam != null && !string.IsNullOrEmpty(tsCablesParam.AsString()))
                {
                    var cableRefs = tsCablesParam.AsString().Split(',');
                    foreach (var cableRef in cableRefs)
                    {
                        var trimmedRef = cableRef.Trim();
                        var cable = TSData?.AllCables?.Values?.FirstOrDefault(c => c.CableRef == trimmedRef);
                        if (cable != null)
                        {
                            segment.Cables.Add(cable);
                            cable.AssociatedTraySegment = segment;
                        }
                    }
                }

                // Calculate capacity and weight (NumberOfCables is computed property)
                segment.Capacity = _trayAnalysisService?.CalculateCapacityUtilization(segment) ?? 0;
                segment.Weight = segment.Cables.Sum(c => c.Weight);

                return segment;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create tray segment model: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Write initial TS_Cables parameter to ensure tray segment persists across loads
        /// </summary>
        private void WriteInitialTSCablesParameter(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null) return;

                // Get current cable references or use empty string for new segments
                var cableRefs = traySegment.GetCablesParameterValue();

                // Store the tray segment and cable data for parameter writing via external event
                SharedData.SelectedTraySegmentForProcessing = traySegment;
                SharedData.ProcessingAction = "WriteTSCablesParameter";

                // Request parameter writing through external event
                MakeRequest(RequestId.WriteTSCablesParameter);

                _logger?.Log($"Requested TS_Cables parameter write for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write initial TS_Cables parameter: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Get tray width from family instance
        /// </summary>
        private double GetTrayWidth(CableTray cableTray)
        {
            try
            {
                var widthParam = cableTray.get_Parameter(BuiltInParameter.CURVE_ELEM_LENGTH) ??
                                cableTray.get_Parameter(BuiltInParameter.GENERIC_WIDTH) ??
                                cableTray.LookupParameter("Width");

                return widthParam?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion
    }
}
