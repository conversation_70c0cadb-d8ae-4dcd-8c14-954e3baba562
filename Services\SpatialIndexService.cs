using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of ISpatialIndexService for efficient geometric queries
    /// </summary>
    public class SpatialIndexService : ISpatialIndexService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the spatial index service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        public SpatialIndexService(Document document, BecaActivityLoggerData logger)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
        }

        #endregion

        #region Index Management

        /// <summary>
        /// Create a spatial index for cable tray segments
        /// </summary>
        public ISpatialIndex<CableTraySegmentModel> CreateTraySegmentIndex(List<CableTraySegmentModel> traySegments, SpatialIndexType indexType = SpatialIndexType.RTree)
        {
            try
            {
                var index = CreateSpatialIndex<CableTraySegmentModel>(indexType);

                foreach (var traySegment in traySegments ?? new List<CableTraySegmentModel>())
                {
                    var boundingBox = traySegment.GetBoundingBox();
                    if (boundingBox != null)
                    {
                        index.Add(traySegment, boundingBox);
                    }
                }

                _logger?.Log($"Created tray segment spatial index with {index.Count} items", LogType.Information);
                return index;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create tray segment spatial index: {ex.Message}", LogType.Error);
                return new SimpleSpatialIndex<CableTraySegmentModel>();
            }
        }

        /// <summary>
        /// Create a spatial index for cables
        /// </summary>
        public ISpatialIndex<CableModel> CreateCableIndex(List<CableModel> cables, SpatialIndexType indexType = SpatialIndexType.RTree)
        {
            try
            {
                var index = CreateSpatialIndex<CableModel>(indexType);

                foreach (var cable in cables ?? new List<CableModel>())
                {
                    var boundingBox = cable.GetRouteBoundingBox();
                    if (boundingBox != null)
                    {
                        index.Add(cable, boundingBox);
                    }
                }

                _logger?.Log($"Created cable spatial index with {index.Count} items", LogType.Information);
                return index;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create cable spatial index: {ex.Message}", LogType.Error);
                return new SimpleSpatialIndex<CableModel>();
            }
        }

        /// <summary>
        /// Create a spatial index for electrical equipment
        /// </summary>
        public ISpatialIndex<Element> CreateEquipmentIndex(List<Element> equipment, SpatialIndexType indexType = SpatialIndexType.RTree)
        {
            try
            {
                var index = CreateSpatialIndex<Element>(indexType);

                foreach (var element in equipment ?? new List<Element>())
                {
                    var boundingBox = element.get_BoundingBox(null);
                    if (boundingBox != null)
                    {
                        index.Add(element, boundingBox);
                    }
                }

                _logger?.Log($"Created equipment spatial index with {index.Count} items", LogType.Information);
                return index;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create equipment spatial index: {ex.Message}", LogType.Error);
                return new SimpleSpatialIndex<Element>();
            }
        }

        /// <summary>
        /// Update an existing spatial index with new or modified items
        /// </summary>
        public bool UpdateSpatialIndex<T>(ISpatialIndex<T> index, List<T> items) where T : class
        {
            try
            {
                if (index == null || items == null) return false;

                foreach (var item in items)
                {
                    var boundingBox = GetBoundingBoxForItem(item);
                    if (boundingBox != null)
                    {
                        // Try to update first, if that fails, add as new
                        if (!index.Update(item, boundingBox))
                        {
                            index.Add(item, boundingBox);
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update spatial index: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Clear and rebuild a spatial index
        /// </summary>
        public bool RebuildSpatialIndex<T>(ISpatialIndex<T> index, List<T> items) where T : class
        {
            try
            {
                if (index == null) return false;

                index.Clear();

                foreach (var item in items ?? new List<T>())
                {
                    var boundingBox = GetBoundingBoxForItem(item);
                    if (boundingBox != null)
                    {
                        index.Add(item, boundingBox);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to rebuild spatial index: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Spatial Queries

        /// <summary>
        /// Find items within a bounding box
        /// </summary>
        public List<T> FindItemsInBoundingBox<T>(ISpatialIndex<T> index, BoundingBoxXYZ boundingBox) where T : class
        {
            try
            {
                return index?.Query(boundingBox) ?? new List<T>();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find items in bounding box: {ex.Message}", LogType.Error);
                return new List<T>();
            }
        }

        /// <summary>
        /// Find items within a specified distance from a point
        /// </summary>
        public List<T> FindItemsNearPoint<T>(ISpatialIndex<T> index, XYZ point, double distance) where T : class
        {
            try
            {
                return index?.Query(point, distance) ?? new List<T>();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find items near point: {ex.Message}", LogType.Error);
                return new List<T>();
            }
        }

        /// <summary>
        /// Find items that intersect with a line segment
        /// </summary>
        public List<T> FindItemsIntersectingLine<T>(ISpatialIndex<T> index, XYZ startPoint, XYZ endPoint, double tolerance = 0.1) where T : class
        {
            try
            {
                // Create bounding box around the line segment
                var min = new XYZ(
                    Math.Min(startPoint.X, endPoint.X) - tolerance,
                    Math.Min(startPoint.Y, endPoint.Y) - tolerance,
                    Math.Min(startPoint.Z, endPoint.Z) - tolerance
                );
                var max = new XYZ(
                    Math.Max(startPoint.X, endPoint.X) + tolerance,
                    Math.Max(startPoint.Y, endPoint.Y) + tolerance,
                    Math.Max(startPoint.Z, endPoint.Z) + tolerance
                );

                var boundingBox = new BoundingBoxXYZ { Min = min, Max = max };
                var candidates = FindItemsInBoundingBox(index, boundingBox);

                // Filter candidates by actual line intersection
                return candidates.Where(item => DoesItemIntersectLine(item, startPoint, endPoint, tolerance)).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find items intersecting line: {ex.Message}", LogType.Error);
                return new List<T>();
            }
        }

        /// <summary>
        /// Find items that intersect with a curve
        /// </summary>
        public List<T> FindItemsIntersectingCurve<T>(ISpatialIndex<T> index, Curve curve, double tolerance = 0.1) where T : class
        {
            try
            {
                if (curve == null) return new List<T>();

                // Use curve bounding box for initial filtering
                var boundingBox = CalculateCurveBoundingBox(curve);
                if (boundingBox == null) return new List<T>();

                // Expand bounding box by tolerance
                boundingBox.Min -= new XYZ(tolerance, tolerance, tolerance);
                boundingBox.Max += new XYZ(tolerance, tolerance, tolerance);

                var candidates = FindItemsInBoundingBox(index, boundingBox);

                // Filter candidates by actual curve intersection
                return candidates.Where(item => DoesItemIntersectCurve(item, curve, tolerance)).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find items intersecting curve: {ex.Message}", LogType.Error);
                return new List<T>();
            }
        }

        /// <summary>
        /// Find the nearest items to a point
        /// </summary>
        public List<SpatialQueryResult<T>> FindNearestItems<T>(ISpatialIndex<T> index, XYZ point, int maxCount = 10) where T : class
        {
            try
            {
                // Start with a small search radius and expand if needed
                double searchRadius = 1.0;
                List<T> candidates = new List<T>();

                // Expand search radius until we have enough candidates or reach a maximum
                while (candidates.Count < maxCount && searchRadius <= 100.0)
                {
                    candidates = FindItemsNearPoint(index, point, searchRadius);
                    searchRadius *= 2.0;
                }

                // Calculate distances and sort
                var results = candidates
                    .Select(item => new SpatialQueryResult<T>
                    {
                        Item = item,
                        Distance = CalculateDistanceToItem(item, point),
                        BoundingBox = GetBoundingBoxForItem(item)
                    })
                    .OrderBy(r => r.Distance)
                    .Take(maxCount)
                    .ToList();

                return results;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find nearest items: {ex.Message}", LogType.Error);
                return new List<SpatialQueryResult<T>>();
            }
        }

        #endregion

        #region Specialized Queries for Cable Trays

        /// <summary>
        /// Find cable trays that could contain a specific cable
        /// </summary>
        public List<CableTraySegmentModel> FindCandidateTraysForCable(ISpatialIndex<CableTraySegmentModel> trayIndex, CableModel cable, double tolerance = 0.1)
        {
            try
            {
                if (cable?.RoutePoints == null || cable.RoutePoints.Count < 2)
                    return new List<CableTraySegmentModel>();

                var candidates = new HashSet<CableTraySegmentModel>();

                // Check each line segment of the cable route
                for (int i = 1; i < cable.RoutePoints.Count; i++)
                {
                    var segmentTrays = FindItemsIntersectingLine(trayIndex, cable.RoutePoints[i - 1], cable.RoutePoints[i], tolerance);
                    foreach (var tray in segmentTrays)
                    {
                        candidates.Add(tray);
                    }
                }

                return candidates.ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find candidate trays for cable: {ex.Message}", LogType.Error);
                return new List<CableTraySegmentModel>();
            }
        }

        /// <summary>
        /// Find cables that could be contained in a specific tray
        /// </summary>
        public List<CableModel> FindCandidateCablesForTray(ISpatialIndex<CableModel> cableIndex, CableTraySegmentModel traySegment, double tolerance = 0.1)
        {
            try
            {
                var trayBoundingBox = traySegment.GetBoundingBox();
                if (trayBoundingBox == null) return new List<CableModel>();

                // Expand bounding box by tolerance
                trayBoundingBox.Min -= new XYZ(tolerance, tolerance, tolerance);
                trayBoundingBox.Max += new XYZ(tolerance, tolerance, tolerance);

                var candidates = FindItemsInBoundingBox(cableIndex, trayBoundingBox);

                // Filter by actual intersection
                return candidates.Where(cable => cable.IntersectsWith(trayBoundingBox, tolerance)).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find candidate cables for tray: {ex.Message}", LogType.Error);
                return new List<CableModel>();
            }
        }

        /// <summary>
        /// Find connected cable trays
        /// </summary>
        public List<CableTraySegmentModel> FindConnectedTrays(ISpatialIndex<CableTraySegmentModel> trayIndex, CableTraySegmentModel traySegment, double connectionTolerance = 0.1)
        {
            try
            {
                var trayBoundingBox = traySegment.GetBoundingBox();
                if (trayBoundingBox == null) return new List<CableTraySegmentModel>();

                // Expand bounding box slightly to find adjacent trays
                trayBoundingBox.Min -= new XYZ(connectionTolerance, connectionTolerance, connectionTolerance);
                trayBoundingBox.Max += new XYZ(connectionTolerance, connectionTolerance, connectionTolerance);

                var candidates = FindItemsInBoundingBox(trayIndex, trayBoundingBox);

                // Remove the source tray and filter by actual connection
                return candidates
                    .Where(t => t != traySegment && AreTraysConnected(traySegment, t, connectionTolerance))
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find connected trays: {ex.Message}", LogType.Error);
                return new List<CableTraySegmentModel>();
            }
        }

        /// <summary>
        /// Find electrical equipment near a cable tray
        /// </summary>
        public List<Element> FindEquipmentNearTray(ISpatialIndex<Element> equipmentIndex, CableTraySegmentModel traySegment, double searchDistance = 5.0)
        {
            try
            {
                var trayCenter = traySegment.GetCenterPoint();
                if (trayCenter == null) return new List<Element>();

                return FindItemsNearPoint(equipmentIndex, trayCenter, searchDistance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find equipment near tray: {ex.Message}", LogType.Error);
                return new List<Element>();
            }
        }

        #endregion

        #region Performance Optimization

        /// <summary>
        /// Optimize spatial index for better query performance
        /// </summary>
        public bool OptimizeSpatialIndex<T>(ISpatialIndex<T> index) where T : class
        {
            try
            {
                // For simple implementation, optimization is not needed
                // In a real R-Tree implementation, this would rebalance the tree
                _logger?.Log("Spatial index optimization completed", LogType.Information);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to optimize spatial index: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Get performance statistics for a spatial index
        /// </summary>
        public SpatialIndexStatistics GetIndexStatistics<T>(ISpatialIndex<T> index) where T : class
        {
            try
            {
                return new SpatialIndexStatistics
                {
                    ItemCount = index?.Count ?? 0,
                    IndexType = index?.IndexType ?? SpatialIndexType.Simple,
                    NodeCount = 1, // Simplified for basic implementation
                    MaxDepth = 1,
                    AverageQueryTime = 0.001,
                    IndexSize = (index?.Count ?? 0) * 64, // Rough estimate
                    BuildTime = 0.1
                };
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get index statistics: {ex.Message}", LogType.Error);
                return new SpatialIndexStatistics();
            }
        }

        /// <summary>
        /// Benchmark spatial index performance
        /// </summary>
        public SpatialIndexBenchmark BenchmarkIndex<T>(ISpatialIndex<T> index, List<SpatialQuery> testQueries) where T : class
        {
            var benchmark = new SpatialIndexBenchmark();

            try
            {
                if (index == null || testQueries == null) return benchmark;

                benchmark.TotalQueries = testQueries.Count;
                var startTime = DateTime.Now;

                foreach (var query in testQueries)
                {
                    var queryStart = DateTime.Now;

                    // Execute query based on type
                    switch (query.QueryType)
                    {
                        case SpatialQueryType.BoundingBox:
                            if (query.BoundingBox != null)
                                FindItemsInBoundingBox(index, query.BoundingBox);
                            break;
                        case SpatialQueryType.PointDistance:
                            if (query.Point != null)
                                FindItemsNearPoint(index, query.Point, query.Distance);
                            break;
                    }

                    var queryTime = (DateTime.Now - queryStart).TotalMilliseconds;
                    benchmark.QueryTimes.Add(queryTime);
                }

                benchmark.TotalTime = (DateTime.Now - startTime).TotalMilliseconds;
                benchmark.AverageTime = benchmark.QueryTimes.Average();
                benchmark.MinTime = benchmark.QueryTimes.Min();
                benchmark.MaxTime = benchmark.QueryTimes.Max();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to benchmark spatial index: {ex.Message}", LogType.Error);
            }

            return benchmark;
        }

        #endregion

        #region Index Persistence

        /// <summary>
        /// Save spatial index to file for later loading
        /// </summary>
        public bool SaveSpatialIndex<T>(ISpatialIndex<T> index, string filePath) where T : class
        {
            try
            {
                // Simplified implementation - in reality you would serialize the index structure
                _logger?.Log($"Spatial index persistence not implemented for {filePath}", LogType.Warning);
                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save spatial index: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Load spatial index from file
        /// </summary>
        public ISpatialIndex<T> LoadSpatialIndex<T>(string filePath) where T : class
        {
            try
            {
                // Simplified implementation - in reality you would deserialize the index structure
                _logger?.Log($"Spatial index loading not implemented for {filePath}", LogType.Warning);
                return new SimpleSpatialIndex<T>();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load spatial index: {ex.Message}", LogType.Error);
                return new SimpleSpatialIndex<T>();
            }
        }

        /// <summary>
        /// Check if a saved spatial index is still valid
        /// </summary>
        public bool IsSavedIndexValid(string filePath, DateTime documentTimestamp)
        {
            try
            {
                // Simplified implementation
                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check saved index validity: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Create a spatial index of the specified type
        /// </summary>
        private ISpatialIndex<T> CreateSpatialIndex<T>(SpatialIndexType indexType) where T : class
        {
            switch (indexType)
            {
                case SpatialIndexType.RTree:
                    // For now, fall back to simple implementation
                    // In a real implementation, you would create an R-Tree
                    return new SimpleSpatialIndex<T>();
                case SpatialIndexType.Quadtree:
                    // For now, fall back to simple implementation
                    return new SimpleSpatialIndex<T>();
                case SpatialIndexType.Grid:
                    // For now, fall back to simple implementation
                    return new SimpleSpatialIndex<T>();
                case SpatialIndexType.Simple:
                default:
                    return new SimpleSpatialIndex<T>();
            }
        }

        /// <summary>
        /// Get bounding box for an item
        /// </summary>
        private BoundingBoxXYZ GetBoundingBoxForItem<T>(T item) where T : class
        {
            switch (item)
            {
                case CableTraySegmentModel traySegment:
                    return traySegment.GetBoundingBox();
                case CableModel cable:
                    return cable.GetRouteBoundingBox();
                case Element element:
                    return element.get_BoundingBox(null);
                default:
                    return null;
            }
        }

        /// <summary>
        /// Check if an item intersects with a line segment
        /// </summary>
        private bool DoesItemIntersectLine<T>(T item, XYZ startPoint, XYZ endPoint, double tolerance) where T : class
        {
            var boundingBox = GetBoundingBoxForItem(item);
            if (boundingBox == null) return false;

            // Simplified intersection test - check if line passes through bounding box
            return LineIntersectsBoundingBox(startPoint, endPoint, boundingBox, tolerance);
        }

        /// <summary>
        /// Check if an item intersects with a curve
        /// </summary>
        private bool DoesItemIntersectCurve<T>(T item, Curve curve, double tolerance) where T : class
        {
            var boundingBox = GetBoundingBoxForItem(item);
            if (boundingBox == null) return false;

            // Simplified intersection test
            try
            {
                var curveBoundingBox = CalculateCurveBoundingBox(curve);
                return BoundingBoxesIntersect(boundingBox, curveBoundingBox, tolerance);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculate distance from a point to an item
        /// </summary>
        private double CalculateDistanceToItem<T>(T item, XYZ point) where T : class
        {
            var boundingBox = GetBoundingBoxForItem(item);
            if (boundingBox == null) return double.MaxValue;

            var center = (boundingBox.Min + boundingBox.Max) * 0.5;
            return point.DistanceTo(center);
        }

        /// <summary>
        /// Check if two trays are connected
        /// </summary>
        private bool AreTraysConnected(CableTraySegmentModel tray1, CableTraySegmentModel tray2, double tolerance)
        {
            var bbox1 = tray1.GetBoundingBox();
            var bbox2 = tray2.GetBoundingBox();

            if (bbox1 == null || bbox2 == null) return false;

            return BoundingBoxesIntersect(bbox1, bbox2, tolerance);
        }

        /// <summary>
        /// Check if a line intersects with a bounding box
        /// </summary>
        private bool LineIntersectsBoundingBox(XYZ start, XYZ end, BoundingBoxXYZ bbox, double tolerance)
        {
            // Expand bounding box by tolerance
            var min = bbox.Min - new XYZ(tolerance, tolerance, tolerance);
            var max = bbox.Max + new XYZ(tolerance, tolerance, tolerance);

            // Simple line-box intersection test
            return (start.X >= min.X && start.X <= max.X && start.Y >= min.Y && start.Y <= max.Y && start.Z >= min.Z && start.Z <= max.Z) ||
                   (end.X >= min.X && end.X <= max.X && end.Y >= min.Y && end.Y <= max.Y && end.Z >= min.Z && end.Z <= max.Z);
        }

        /// <summary>
        /// Check if two bounding boxes intersect
        /// </summary>
        private bool BoundingBoxesIntersect(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2, double tolerance)
        {
            return bbox1.Max.X + tolerance >= bbox2.Min.X && bbox1.Min.X - tolerance <= bbox2.Max.X &&
                   bbox1.Max.Y + tolerance >= bbox2.Min.Y && bbox1.Min.Y - tolerance <= bbox2.Max.Y &&
                   bbox1.Max.Z + tolerance >= bbox2.Min.Z && bbox1.Min.Z - tolerance <= bbox2.Max.Z;
        }

        /// <summary>
        /// Calculate bounding box for a curve by sampling points
        /// </summary>
        private BoundingBoxXYZ CalculateCurveBoundingBox(Curve curve, int sampleCount = 50)
        {
            try
            {
                if (curve == null) return null;

                var points = new List<XYZ>();

                // Add start and end points
                points.Add(curve.GetEndPoint(0));
                points.Add(curve.GetEndPoint(1));

                // Sample points along the curve
                double parameterRange = curve.GetEndParameter(1) - curve.GetEndParameter(0);
                double stepSize = parameterRange / (sampleCount - 1);

                for (int i = 1; i < sampleCount - 1; i++)
                {
                    double parameter = curve.GetEndParameter(0) + i * stepSize;
                    try
                    {
                        var point = curve.Evaluate(parameter, false);
                        points.Add(point);
                    }
                    catch
                    {
                        // Skip invalid parameters
                    }
                }

                // Calculate bounding box from all points
                if (points.Count == 0) return null;

                var min = new XYZ(
                    points.Min(p => p.X),
                    points.Min(p => p.Y),
                    points.Min(p => p.Z)
                );

                var max = new XYZ(
                    points.Max(p => p.X),
                    points.Max(p => p.Y),
                    points.Max(p => p.Z)
                );

                return new BoundingBoxXYZ { Min = min, Max = max };
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating curve bounding box: {ex.Message}", LogType.Error);
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// Simple spatial index implementation using linear search
    /// This is a basic implementation for initial development
    /// </summary>
    /// <typeparam name="T">Type of items stored in the index</typeparam>
    public class SimpleSpatialIndex<T> : ISpatialIndex<T> where T : class
    {
        #region Fields

        private readonly List<SpatialIndexItem<T>> _items;

        #endregion

        #region Properties

        /// <summary>
        /// Get the number of items in the spatial index
        /// </summary>
        public int Count => _items.Count;

        /// <summary>
        /// Get the type of spatial index
        /// </summary>
        public SpatialIndexType IndexType => SpatialIndexType.Simple;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize a new simple spatial index
        /// </summary>
        public SimpleSpatialIndex()
        {
            _items = new List<SpatialIndexItem<T>>();
        }

        #endregion

        #region ISpatialIndex Implementation

        /// <summary>
        /// Add an item to the spatial index
        /// </summary>
        public void Add(T item, BoundingBoxXYZ boundingBox)
        {
            if (item == null || boundingBox == null) return;

            _items.Add(new SpatialIndexItem<T>
            {
                Item = item,
                BoundingBox = boundingBox
            });
        }

        /// <summary>
        /// Remove an item from the spatial index
        /// </summary>
        public bool Remove(T item)
        {
            if (item == null) return false;

            var itemToRemove = _items.FirstOrDefault(i => ReferenceEquals(i.Item, item));
            if (itemToRemove != null)
            {
                _items.Remove(itemToRemove);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Update an item's position in the spatial index
        /// </summary>
        public bool Update(T item, BoundingBoxXYZ newBoundingBox)
        {
            if (item == null || newBoundingBox == null) return false;

            var existingItem = _items.FirstOrDefault(i => ReferenceEquals(i.Item, item));
            if (existingItem != null)
            {
                existingItem.BoundingBox = newBoundingBox;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Clear all items from the spatial index
        /// </summary>
        public void Clear()
        {
            _items.Clear();
        }

        /// <summary>
        /// Query items within a bounding box
        /// </summary>
        public List<T> Query(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return new List<T>();

            return _items
                .Where(item => BoundingBoxesIntersect(item.BoundingBox, boundingBox))
                .Select(item => item.Item)
                .ToList();
        }

        /// <summary>
        /// Query items within a distance from a point
        /// </summary>
        public List<T> Query(XYZ point, double distance)
        {
            if (point == null) return new List<T>();

            return _items
                .Where(item => PointIntersectsBoundingBox(point, item.BoundingBox, distance))
                .Select(item => item.Item)
                .ToList();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Check if two bounding boxes intersect
        /// </summary>
        private bool BoundingBoxesIntersect(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2)
        {
            if (bbox1 == null || bbox2 == null) return false;

            return bbox1.Max.X >= bbox2.Min.X && bbox1.Min.X <= bbox2.Max.X &&
                   bbox1.Max.Y >= bbox2.Min.Y && bbox1.Min.Y <= bbox2.Max.Y &&
                   bbox1.Max.Z >= bbox2.Min.Z && bbox1.Min.Z <= bbox2.Max.Z;
        }

        /// <summary>
        /// Check if a point is within distance of a bounding box
        /// </summary>
        private bool PointIntersectsBoundingBox(XYZ point, BoundingBoxXYZ bbox, double distance)
        {
            if (point == null || bbox == null) return false;

            // Calculate closest point on bounding box to the query point
            var closestX = Math.Max(bbox.Min.X, Math.Min(point.X, bbox.Max.X));
            var closestY = Math.Max(bbox.Min.Y, Math.Min(point.Y, bbox.Max.Y));
            var closestZ = Math.Max(bbox.Min.Z, Math.Min(point.Z, bbox.Max.Z));

            var closestPoint = new XYZ(closestX, closestY, closestZ);
            return point.DistanceTo(closestPoint) <= distance;
        }

        #endregion
    }

    /// <summary>
    /// Item stored in the spatial index
    /// </summary>
    /// <typeparam name="T">Type of item</typeparam>
    internal class SpatialIndexItem<T> where T : class
    {
        public T Item { get; set; }
        public BoundingBoxXYZ BoundingBox { get; set; }
    }
}
